from odoo import models, fields, api, _

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    invoice_status = fields.Selection(string="Invoice Status", readonly=False)

    can_edit_invoice_status = fields.Boolean(
        compute="_compute_can_edit_invoice_status",
        default=lambda self: self.env.user.has_group('ro_sale_add_status.group_edit_invoice_status'),
    )

    @api.depends('partner_id')
    def _compute_can_edit_invoice_status(self):
        for record in self:
            record.can_edit_invoice_status = self.env.user.has_group('ro_sale_add_status.group_edit_invoice_status')