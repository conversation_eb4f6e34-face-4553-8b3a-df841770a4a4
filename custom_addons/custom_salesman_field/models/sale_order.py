# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    ro_salesman_id = fields.Many2one(
        'hr.employee',
        string='Salesman',
        help='Employee responsible for this sale order as salesman'
    )

    @api.onchange('partner_id', 'partner_id.ro_salesman_id')
    def _onchange_partner_id_salesman(self):
        """Auto-fill salesman from partner when partner is selected"""
        if self.partner_id and self.partner_id.ro_salesman_id:
            self.ro_salesman_id = self.partner_id.ro_salesman_id
