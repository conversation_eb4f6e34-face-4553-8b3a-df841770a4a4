# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResPartner(models.Model):
    _inherit = 'res.partner'

    ro_salesman_id = fields.Many2one(
        'hr.employee',
        string='Salesman',
        help='Employee responsible for this partner as salesman'
    )

    @api.model
    def _commercial_fields(self):
        """Add ro_salesman_id to commercial fields so it's inherited by contacts"""
        return super()._commercial_fields() + ['ro_salesman_id']
