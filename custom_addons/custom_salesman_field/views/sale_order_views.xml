<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add Salesman field to Sale Order form view -->
        <record id="sale_order_form_salesman_inherit" model="ir.ui.view">
            <field name="name">sale.order.form.salesman.inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <field name="user_id" position="after">
                    <field name="ro_salesman_id" options="{'no_create': True, 'no_open': True}"/>
                </field>
            </field>
        </record>

        <!-- Add Salesman field to Sale Order tree view -->
        <record id="sale_order_tree_salesman_inherit" model="ir.ui.view">
            <field name="name">sale.order.tree.salesman.inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <field name="user_id" position="after">
                    <field name="ro_salesman_id" optional="hide"/>
                </field>
            </field>
        </record>
    </data>
</odoo>
