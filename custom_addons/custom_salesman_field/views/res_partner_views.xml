<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add Salesman field to Partner form view -->
        <record id="res_partner_form_salesman_inherit" model="ir.ui.view">
            <field name="name">res.partner.form.salesman.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <group name="sale" position="inside">
                    <field name="ro_salesman_id" options="{'no_create': True, 'no_open': True}"/>
                </group>
            </field>
        </record>

        <!-- Add Salesman field to Partner tree view -->
        <record id="res_partner_tree_salesman_inherit" model="ir.ui.view">
            <field name="name">res.partner.tree.salesman.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_tree"/>
            <field name="arch" type="xml">
                <field name="display_name" position="after">
                    <field name="ro_salesman_id" optional="hide"/>
                </field>
            </field>
        </record>
    </data>
</odoo>
