# -*- coding: utf-8 -*-
{
    'name': "Custom Salesman Field",
    'summary': """
        Add Salesman field to Partner and Sale Order models
        """,
    'description': """
        This module adds a Salesman field (many2one to hr.employee) to both:
        - res.partner (Customer/Vendor)
        - sale.order (Sale Order)
        
        The field allows linking customers and sales orders to specific employees as salesmen.
    """,
    'author': "Custom Development",
    'website': "https://www.example.com",
    'category': 'Sales',
    'version': '********.0',
    'depends': [
        'base',
        'sale',
        'hr',
        'contacts',
    ],
    'data': [
        'views/res_partner_views.xml',
        'views/sale_order_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
}
