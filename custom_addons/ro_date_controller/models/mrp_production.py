from odoo import models, fields
from datetime import timedelta

class MrpProduction(models.Model):
    _inherit = 'mrp.production'
    
    ro_decremented_once = fields.Boolean(default=False)

    def action_decrement_date_start(self):
        for production in self:
            if production.date_planned_start:
                production.date_planned_start -= timedelta(days=1)
                production.ro_decremented_once = True
