<odoo>
    <record id="view_order_form_inherit" model="ir.ui.view">
        <field name="name">view.order.form.inherited</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='medium_id']" position="attributes">   
                <attribute name="invisible">1</attribute>     
                   </xpath>
                   <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="medium_id" />
                </xpath>
                <xpath expr="//field[@name='medium_id']" position="attributes">
                     <attribute name="string">Country</attribute>     
                </xpath>
            <xpath expr="//field[@name='medium_id']" position="after">
                <field name="ro_ship_name"></field>
            </xpath>
            <xpath expr="//field[@name='ro_ship_name']" position="after">
                <field name="ro_product_type_id"></field>
            </xpath>
        </field>
    </record>


    
        <record id="sale_order_product_types_action" model="ir.actions.act_window">
            <field name="name">Product Types</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.type</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create Your Product Types
                </p>
            </field>
        </record>
    
        <menuitem id="sale_order_template_menu_inherited"
        name="Product Types"
        action="sale_order_product_types_action"
        parent="sale.menu_sales_config"
        sequence="60"/>

</odoo>