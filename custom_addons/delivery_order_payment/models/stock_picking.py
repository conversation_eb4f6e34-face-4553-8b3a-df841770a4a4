# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import json
import math
from odoo.addons.stock.models.stock_picking import Picking

def _set_scheduled_date(self):
    for picking in self:
        if picking.state in ('done', 'cancel'):
            pass
            # raise UserError(_("You cannot change the Scheduled Date on a done or cancelled transfer."))
        else:
            picking.move_ids.write({'date': picking.scheduled_date})
Picking._set_scheduled_date = _set_scheduled_date


PAYMENT_STATE_SELECTION = [
    ('not_paid', 'Not Paid'),
    ('paid', 'Paid'),
    ('partial', 'Partially Paid')
]

class StockPickingType(models.Model):
    _inherit = 'stock.picking.type'

    is_load_transaction = fields.Boolean()
class StockPicking(models.Model):
    _inherit = 'stock.picking'

    driver_name = fields.Char()
    governorate_name = fields.Char()
    car_num = fields.Char()
    trailer_num = fields.Char()
    scale_num = fields.Char()

    is_load_transaction = fields.Boolean(related='picking_type_id.is_load_transaction')

    payment_ids = fields.One2many(
        string='Payments',
        comodel_name='account.payment',
        inverse_name='picking_id',
        tracking=True
    )

    currency_id = fields.Many2one('res.currency', related='sale_id.currency_id')

    amount_paid_text = fields.Text(string="Amount Paid TXT", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    amount_paid = fields.Monetary(currency_field='currency_id', string="Amount Paid", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    amount_due = fields.Monetary(currency_field='currency_id', string="Amount Due", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    payment_state = fields.Selection(PAYMENT_STATE_SELECTION, string="Payment Status", store=True,
                                     readonly=True, copy=False, tracking=True, compute='_compute_payment')
                                     
    payment_count = fields.Integer("Payments", compute='_compute_payment', store=True,
                                  readonly=True, copy=False)

    
    def action_picking_open_payments(self):
        action = self.env["ir.actions.actions"]._for_xml_id("account.action_account_payments")
        action['domain'] = [('picking_id', '=', self.id)]
        return action
    

    # def _action_done(self):
    #     res = super(StockPicking, self)._action_done()
    #     for picking in self:
        
    #         # reflict update in sale line from picking
    #         sign = 1
    #         if picking.picking_type_code == 'incoming':
    #             sign = -1
    #         for line in picking.move_ids:
    #             line.sale_line_id.to_be_invoiced_qty += math.ceil(line.quantity_done) * sign
    #         for so_line in picking.sale_id.order_line.filtered(lambda x:x.product_id.is_load_service):
    #             so_line.to_be_invoiced_qty += 1 * sign

    #     return res

    @api.depends('payment_ids','move_ids','state')
    def _compute_payment(self):
        for order in self:
            done_qty =  sum(order.move_ids.mapped(lambda x:math.ceil(x.quantity_done)))
            total = (done_qty*20) + 39

            if order.payment_ids:
                payment_ids = order.payment_ids.filtered(lambda x:x.state=='posted')
                
                pay_text = ''
                amount_total = 0

                for payment in payment_ids:

                    if payment.currency_id and payment.currency_id != order.currency_id:
                        amount = payment.currency_id._convert(
                            payment.amount , order.currency_id, order.company_id, payment.date or fields.Date.context_today(order))
                        pay_text += '%s  %s%s/%s%s \n'%(payment.name, payment.amount, payment.currency_id.symbol, amount, order.currency_id.symbol)   
                    else:
                        amount = payment.amount
                        pay_text += '%s  %s%s \n'%(payment.name, payment.amount, payment.currency_id.symbol)
                    
                    amount_total += amount

                
                amount_due = total - amount_total

                if amount_due == 0:
                    order.payment_state = 'paid'
                else:
                    order.payment_state = 'partial'
                
                order.amount_due = amount_due
                order.amount_paid = amount_total
                order.payment_count = len(payment_ids)

                
                    
                order.amount_paid_text = pay_text
            else:
                order.payment_state = 'not_paid'
                order.amount_due = total
                order.amount_paid = 0
                order.payment_count = 0
                order.amount_paid_text = ''
                

    def action_register_payment(self):
        return {
            'name': _('Register Payment'),
            'res_model': 'picking.payment.register',
            'view_mode': 'form',
            'context': {
                'default_picking_id': self.id,
                'active_model': 'stock.picking',
                'active_ids': self.ids,
            },
            'target': 'new',
            'type': 'ir.actions.act_window',
        }


class StockReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'

    def _create_returns(self):
        new_picking, pick_type_id = super(StockReturnPicking, self)._create_returns()
        for payment in self.picking_id.payment_ids:
            payment.action_cancel()
        return new_picking, pick_type_id
