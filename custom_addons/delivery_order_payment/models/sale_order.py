# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import math


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    hide_create_invoice = fields.Boolean(compute="get_check_hide_create_invoice")

    def get_check_hide_create_invoice(self):
        for rec in self:
            rec.hide_create_invoice = False
            
            sum_to_be_invoice = sum(rec.order_line.mapped('to_be_invoiced_qty'))

            sum_qty = sum(rec.order_line.filtered(lambda x:x.product_id.detailed_type == 'product').mapped('product_uom_qty'))
            sum_delivered = sum(rec.order_line.filtered(lambda x:x.product_id.detailed_type == 'product').mapped('qty_delivered'))


            sum_invoiced = sum(rec.order_line.filtered(lambda x:x.product_id.detailed_type == 'product').mapped('qty_invoiced'))

            if sum_to_be_invoice == 0 and sum_qty == sum_delivered and sum_qty <= sum_invoiced:
                rec.hide_create_invoice = True


    total_to_invoice = fields.Float(compute="get_total_to_invoice", store=True) 

    @api.depends('order_line.to_be_invoiced_qty','order_line.amount_to_be_invoiced')
    def get_total_to_invoice(self):
        for rec in self:
            rec.total_to_invoice = sum(rec.order_line.mapped('amount_to_be_invoiced'))

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    to_be_invoiced_qty = fields.Float(compute="get_amount_to_be_invoiced", store=True)
    amount_to_be_invoiced = fields.Float(compute="get_amount_to_be_invoiced", store=True)

    @api.depends('move_ids','qty_invoiced','qty_delivered')
    def get_amount_to_be_invoiced(self):
        for rec in self:
            rec.to_be_invoiced_qty = 0
            rec.amount_to_be_invoiced = 0

            out_moves = rec.move_ids.filtered(lambda x:x.picking_id.picking_type_code == 'outgoing' and x.state == 'done')
            return_moves = rec.move_ids.filtered(lambda x:x.picking_id.picking_type_code == 'incoming' and x.state == 'done')

            all_out_moves = rec.order_id.order_line.move_ids.filtered(lambda x:x.picking_id.picking_type_code == 'outgoing' and x.state == 'done')
            all_return_moves = rec.order_id.order_line.move_ids.filtered(lambda x:x.picking_id.picking_type_code == 'incoming' and x.state == 'done')

            delivered_qty = 0
            if rec.product_id.detailed_type == 'product':
                delivered_qty = sum(out_moves.mapped(lambda x:math.ceil(x.quantity_done))) - sum(return_moves.mapped(lambda x:math.ceil(x.quantity_done)))
            elif rec.product_id.detailed_type in ('service','consu'):
                delivered_qty = len(all_out_moves) - len(all_return_moves)

            rec.to_be_invoiced_qty = delivered_qty - rec.qty_invoiced
            rec.amount_to_be_invoiced = rec.to_be_invoiced_qty * rec.price_unit

    def _prepare_invoice_line(self, **optional_values):
        res = super(SaleOrderLine, self)._prepare_invoice_line(**optional_values)

        has_load_service = any(self.order_id.order_line.mapped('product_id.is_load_service'))
        if has_load_service:
            res['quantity'] = self.to_be_invoiced_qty
            # self.to_be_invoiced_qty = 0
        return res
