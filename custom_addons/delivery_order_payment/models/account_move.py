# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class AccountMove(models.Model):
    _inherit = 'account.move'

    def _post(self, soft=True):
        posted = super()._post(soft)

        for invoice in posted.filtered(lambda move: move.is_invoice()):
            order_id = self.line_ids.sale_line_ids.order_id
            if order_id:
                payments = order_id.picking_ids.payment_ids
                move_lines = payments.line_ids.filtered(lambda line: line.account_type in ('asset_receivable', 'liability_payable') and not line.reconciled)
                for line in move_lines:
                    invoice.js_assign_outstanding_line(line.id)
        return posted