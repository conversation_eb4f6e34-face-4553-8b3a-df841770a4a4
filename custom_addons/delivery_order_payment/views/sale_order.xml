<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_sale_order_form_invoice_paid" model="ir.ui.view">
            <field name="name">view.sale.form.invoice.paid</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='order_line']/tree/field[@name='product_uom_qty']" position="after">
                    <field name="to_be_invoiced_qty" attrs="{'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                </xpath>
                
                <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="before">
                    <field name="amount_to_be_invoiced" attrs="{'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]}" optional="show"/>
                </xpath>

                <xpath expr="//button[@id='create_invoice']" position="after">
                    <field name="hide_create_invoice" invisible="1"/>
                </xpath>

                <xpath expr="//button[@id='create_invoice']" position="attributes">
                    <attribute name="attrs">{'invisible': ['|',('invoice_status', '!=', 'to invoice'),('hide_create_invoice', '=', True)]}</attribute>
                </xpath>

            </field>
        </record>

        <record id="view_sale_order_tree_invoice_paid" model="ir.ui.view">
            <field name="name">view.sale.order.form.invoice.paid</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='amount_total']" position="before">
                    <field name="total_to_invoice" sum="Total To Invoice" optional="show"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>