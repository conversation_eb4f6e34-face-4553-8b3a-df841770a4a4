<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_picking_type_form" model="ir.ui.view">
            <field name="name">view.picking.type.form</field>
            <field name="model">stock.picking.type</field>
            <field name="inherit_id" ref="stock.view_picking_type_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='show_reserved']" position="after">
                    <field name="is_load_transaction" />
                </xpath>
            </field>
        </record>

        <record id="view_picking_form_register_payment" model="ir.ui.view">
            <field name="name">view.picking.form.register.payment</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="attributes">
                    <attribute name="duplicate">0</attribute>
                </xpath>

                <xpath expr="//field[@name='origin']" position="after">
                    <field name="is_load_transaction" invisible="1"/>

                    <field name="driver_name" string="أسم السائق" attrs="{'invisible': [('is_load_transaction','=',False)]}"/>
                    <field name="governorate_name" string="المحافظة" attrs="{'invisible': [('is_load_transaction','=',False)]}"/>
                    <field name="car_num" string="رقم السيارة" attrs="{'invisible': [('is_load_transaction','=',False)]}"/>
                    <field name="trailer_num" string="رقم المقطورة" attrs="{'invisible': [('is_load_transaction','=',False)]}"/>
                    <field name="scale_num" string="رقم الميزان" attrs="{'invisible': [('is_load_transaction','=',False)]}"/>
                </xpath>

                <xpath expr="//field[@name='state']" position="before">
                    <field name="payment_state" invisible="1"/>
                    <button name="action_register_payment"
                                type="object" class="oe_highlight"
                                attrs="{'invisible': ['|', '|', '|', ('is_load_transaction','=',False), ('state', '!=', 'done'), ('picking_type_code', '!=', 'outgoing'), ('payment_state', 'not in', ('not_paid', 'partial'))]}"
                                string="Register Payment" data-hotkey="r"
                                groups="sales_team.group_sale_salesman"/>
                </xpath>
                <div name="button_box" position="inside">
                    <button class="oe_stat_button" type="object" name="action_picking_open_payments" context="{'default_picking_id': active_id}" icon="fa-money" attrs="{'invisible': [('is_load_transaction','=',False)]}">
                        <div class="o_stat_info">
                            <field name="payment_count" class="o_stat_value"/>
                            <span class="o_stat_text">Payments</span>
                        </div>
                    </button>
                </div>
                <div name="button_box" position="after">
                    <widget name="web_ribbon" title="Paid" attrs="{'invisible': [('payment_state', '!=', 'paid')]}"/>
                    <widget name="web_ribbon" title="Partial" attrs="{'invisible': [('payment_state', '!=', 'partial')]}"/>
                </div>
                <xpath expr="//page[@name='operations']" position="inside">

                    <group name="note_group" col="6" class="mt-2 mt-md-0" attrs="{'invisible': ['|', '|', ('is_load_transaction','=',False), ('state', '!=', 'done'), ('picking_type_code', '!=', 'outgoing')]}">
                        <group colspan="4">
                        </group>
                        <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_picking_total">
                            <field name="amount_paid_text" colspan="2" nolabel="1"/>
                            <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                                <label for="amount_paid"/>
                            </div>
                            <field name="amount_paid" nolabel="1" class="oe_subtotal_footer_separator" widget='monetary'
                                    options="{'currency_field': 'currency_id'}"/>
                            <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                                <label for="amount_due"/>
                            </div>
                            <field name="amount_due" nolabel="1" class="oe_subtotal_footer_separator" widget='monetary'
                                    options="{'currency_field': 'currency_id'}"/>

                        </group>
                        <div class="clearfix"/>
                    </group>

                </xpath>
            </field>
        </record>
        
        <record id="ro_stock_picking_tree_delivery" model="ir.ui.view">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.vpicktree"/>
            <field name="arch" type="xml">
              <xpath expr="//tree" position="inside">
                <field name="driver_name" string="أسم السائق"  optional='hide'/>
                <field name="governorate_name" string="المحافظة" optional='hide'/>
                <field name="car_num" string="رقم السيارة" optional='hide'/>
                <field name="trailer_num" string="رقم المقطورة" optional='hide'/>
                <field name="scale_num" string="رقم الميزان" optional='hide'/>
              </xpath>
            </field>
        </record>

    </data>
</odoo>