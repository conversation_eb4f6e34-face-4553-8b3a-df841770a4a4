<odoo>
    <record id="report_account_assets" model="ir.actions.report">
        <field name="name">Defered Expense Report</field>
        <field name="model">account.asset</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ro_expense_report.report_asset_template</field>
        <field name="report_file">ro_expense_report.report_asset_template</field>
        <field name="print_report_name">'Expense:%s' % (object.name)</field>
        <field name="binding_model_id" ref="account_asset.model_account_asset"/>
        <field name="binding_type">report</field>
    </record>

        <!-- <record id="view_form_account_asset_expense" model="ir.ui.view">
            <field name="name">account.asset.asset.expense</field>
            <field name="model">account.asset</field>
            <field name="inherit_id" ref="account_asset.view_account_asset_revenue_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="%(ro_expense_report.report_account_assets)d"
                            type="action"
                            string="New Deferred Expense Report"
                            class="oe_highlight"
                            options="{'toolbar':'print'}"/>
                </xpath>
            </field>
        </record> -->
</odoo>