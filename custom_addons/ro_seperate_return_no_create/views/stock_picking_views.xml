<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_return_picking_tree" model="ir.ui.view">
        <field name="name">stock.picking.tree.return.no.create</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.vpicktree"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="create">false</attribute>
                <attribute name="multi_edit">false</attribute>
                <attribute name="delete">false</attribute>
            </tree>
        </field>
    </record>

    <record model="ir.actions.act_window.view" id="transfer_in_out.action_document_return_picking_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_return_picking_tree"/>
        <field name="act_window_id" ref="transfer_in_out.action_transfer_return_picking"/>
    </record>

    <record id="view_stock_picking_form_no_create_delete" model="ir.ui.view">
        <field name="name">stock.picking.form.return.no.create</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="transfer_in_out.view_stock_picking_form_own"/>
        <field name="arch" type="xml">
            <form position="attributes">
                <attribute name="create">false</attribute>
                <attribute name="delete">false</attribute>
            </form>
        </field>
    </record>
</odoo>