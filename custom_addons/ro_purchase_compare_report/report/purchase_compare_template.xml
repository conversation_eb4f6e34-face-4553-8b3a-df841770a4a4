<odoo>
    <template id="purchase_compare_template">
      <t t-call="web.html_container">
        <t t-call="web.external_layout">
          <div class="page">
            <h3 class="text-center header"><u>إداة المشتريات</u></h3>
          </div>
          <table class="table table-sm " style="border: none; margin-top:100px;  direction: rtl; width: 100%;" >
            <tr style="border: none;">
                <td style="border: none; direction: ltr;">  <p style="text-align:right;"><strong>محضر ترسية عرض أسعار رقم</strong></p></td>
                <td style="border: none;"><strong style="margin-left:150px;">(<span t-field="docs[0].name" ></span>)</strong>
              </td>
            </tr>
         </table>
         <table class="table table-sm table-bordered table-hover">
          <thead>
            <tr>
              <th>Product</th>
              <th>quantity</th>
              <t t-foreach="docs" t-as="po">
                <th>
                  <t t-esc="po.partner_id.display_name"/>
                </th>
              </t>
            </tr>
          </thead>
          <tbody>
            <t t-foreach="docs[0].order_line.mapped('product_id')" t-as="product">
              <tr>
                <td>
                  <t t-esc="product.display_name"/>
                </td>
                <t t-if="docs[0].order_line and docs[0].order_line.filtered(lambda l: l.product_id.id == product.id)[0]">
                  <td>
                    <t t-esc="docs[0].order_line.filtered(lambda l: l.product_id.id == product.id)[0].product_qty"/>
                  </td>
                </t>
                <t t-else="">
                  -
                </t>
                <t t-foreach="docs" t-as="po">
                  <td>
                    <t t-set="lines" t-value="po.order_line.filtered(lambda l: l.product_id.id == product.id)"/>
                    <t t-if="lines">
                      <div>
                        Price: <t t-esc="lines[0].price_unit"/>
                      </div>
                    </t>
                    <t t-else="">
                      -
                    </t>
                  </td>
                </t>
              </tr>
            </t>
          </tbody>
        </table>
          <table class="table table-sm " style="border: none; margin-top:35px;  direction: rtl;" >
              <tr style="border: none;">
                <td style="border: none; direction: ltr;"> <p  style="margin-top:30px; text-align:right;"><strong>( <span t-field="docs[0].name" ></span>
                  ) تري اللجه أن شراء البنود  السابقه حسب عرض اسعار رقم </strong></p></td>
                <td style="border: none;"><p  style="margin-right:50px;text-align:left;  "><strong> من المورد</strong></p></td>
                <td style="border: none;"><p  style="margin-left:30px;text-align:left; "><strong><span t-field="docs[0].partner_id" ></span></strong></p></td>
              </tr>
          </table>

          <p class="text-center"> <strong>حيث انها <u>انسب وارخص</u>الاسعار كما هو مبين في كشوفات عروض الاسعار </strong></p>

          <table class="table table-sm text-center" style="border: none; margin-top:35px; direction: rtl;" >
              <tr style="border: none;">
                <td style="border: none;"><b>عضو مالي</b></td>
                <td style="border: none;"><b>عضو لجنة المشتريات ومقرر اللجنه</b>  </td>
                <td style="border: none;"><b>يعتمد</b></td>
              </tr>
              <tr style="border: none;">
                <td style="border: none; padding-left:60px;"><b>أ/</b></td>
                <td style="border: none;padding-left:210px;"><b>أ/</b></td>
                <td style="border: none;"><b>عضو اللجنه الهندسية:أ/</b></td>
              </tr>
          </table>
        </t>
      </t>
    </template>
  </odoo>
  