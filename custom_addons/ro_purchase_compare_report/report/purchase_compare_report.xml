<odoo>
    <record id="paperformat_survey_certification" model="report.paperformat">
        <field name="name">Survey Certification</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Landscape</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">0</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">96</field>
    </record>
    <record id="action_report_purchase_compare" model="ir.actions.report">
        <field name="name">RFQ Comparison Report</field>
        <field name="model">purchase.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ro_purchase_compare_report.purchase_compare_template</field>
        <field name="report_file">ro_purchase_compare_report.purchase_compare_template</field>
        <field name="print_report_name">'RFQ Comparison'</field>
        <field name="paperformat_id" ref="paperformat_survey_certification"/>
        <field name="binding_model_id" ref="purchase.model_purchase_order"/>
        <field name="binding_type">report</field>
    </record>
</odoo>