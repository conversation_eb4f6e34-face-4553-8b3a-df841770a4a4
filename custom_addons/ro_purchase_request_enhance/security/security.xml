<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="0">
        <record id="ro_purchase_request_enhance.module_category_purchase_request" model="ir.module.category">
            <field name="description">Helps you handle your purchase request orders.</field>
            <field name="name">Purchase Request</field>
            <field name="sequence">9</field>
        </record>


        <record id="group_purchase_request_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="ro_purchase_request_enhance.module_category_purchase_request"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">the user will have access to his own data in the purchase request application.</field>
        </record>

        <record id="group_purchase_request_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="comment">the user will have an access to the purchase request configuration.</field>
            <field name="category_id" ref="ro_purchase_request_enhance.module_category_purchase_request"/>
            <field name="implied_ids" eval="[(4, ref('group_purchase_request_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

    </data>
</odoo>
