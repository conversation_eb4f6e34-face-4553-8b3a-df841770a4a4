# -*- coding: utf-8 -*-

from odoo import fields, models, api, _, exceptions, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError
    
class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    analytic_account_id = fields.Many2one('account.analytic.account',copy=False, check_company=True,  # Unrequired company
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
