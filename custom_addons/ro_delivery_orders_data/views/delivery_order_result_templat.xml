<odoo>
    <record id="delivery_order_report_action" model="ir.actions.report">
        <field name="name">Delivery Orders Report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ro_delivery_orders_data.delivery_order_template</field>
        <field name="report_file">ro_delivery_orders_data.delivery_order_template</field>
        <field name="print_report_name">'Delivery Orders Report'</field>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>
    <template id="delivery_order_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <div class="page">
                    <t t-set="today_date" t-value="datetime.datetime.now()"/>
                    <t t-set="customer_name" t-value="docs[0].sale_id.partner_id.display_name"/>
                    <t t-set="ship_name" t-value="docs[0].sale_id.ro_ship_name"/>
                        <div class="page">
                            <h3 class="text-center">بيان بالبضائع المنصرفة</h3>
                                <div class="text-center bt-3 mb-3">
                                    <b><t t-esc="docs[-1].display_name"/></b> : رقم طلب الصرف  
                                </div> 
                                <div class="row" style="margin-left:26%; margin-bottom: 20px;">
                                    <div class="col-4" style="margin-right:50px;">
                                        <b>From: <t t-esc="docs[-1].scheduled_date.strftime('%d-%b-%y').upper()"/></b>
                                    </div>
                                    <div class="col-4">
                                        <b>To: <t t-esc="docs[0].scheduled_date.strftime('%d-%b-%y').upper()"/></b>
                                    </div>
                                </div>
                                <div class="text-center bt-3 mb-3">
                                    <t t-set="total_qty" t-value="docs[0].sale_id.order_line[0].product_uom_qty if docs[0].sale_id and docs[0].sale_id.order_line else 0"/>
                                    <b><t t-esc="total_qty"/></b> : كمية الافراج الجمركي   
                                </div> 
                            <p style="text-align:right"><t t-esc="docs[0].origin"/> : رقم الافراج الجمركي </p>
                            <p style="text-align:right"><t t-esc="docs[0].sale_id.partner_id.ref"/> : كود العميل </p>
                            <div class="mb-2" style="display: flex; flex-direction: row-reverse; gap: 10px;">
                                <span> : اسم العميل</span>
                                <span><t t-esc="customer_name"/></span>
                            </div>
                            <p style="text-align:right"><t t-esc="ship_name"/> : اسم الباخرة</p>
                            <table class="table table-sm table-bordered" style="width: 100%; font-size: 13px; margin-bottom: 30px;">
                                <thead>
                                    <tr>
                                        <th class="text-center">وقت الخروج</th>
                                        <th class="text-center">المخزن</th>
                                        <th class="text-center">الأجولة</th>
                                        <th class="text-center">وحدة القياس</th>
                                        <th class="text-center">الكمية</th>
                                        <th class="text-center">كارتة الميزان</th>
                                        <th class="text-center">كارتة التوجية</th>
                                        <th class="text-center">مقاول النقل</th>
                                        <th class="text-center">رقم السيارة</th>
                                        <th class="text-center">اسم السائق</th>
                                        <th class="text-center">كود الصنف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="docs.filtered(lambda picking: picking.date_done)" t-as="picking">
                                        <t t-set="warehouse_name" t-value="picking.picking_type_id.warehouse_id.display_name if picking.picking_type_id.warehouse_id else False"/>
                                        <t t-set="location_name" t-value="picking.location_id.display_name if picking.location_id else False"/>
                                        <t t-foreach="picking.move_ids_without_package" t-as="picking_line">
                                            <t t-set="product_uom_qty" t-value="picking_line.quantity_done" />
                                            <t t-set="bags_count" t-value="0" />
                                            <t t-set="weighing_card" t-value="picking.scale_num"/>
                                            <t t-set="routing_card" t-value="picking.display_name" />
                                            <t t-set="transport_contractor_name" t-value="picking.partner_id.display_name" />
                                            <t t-set="car_num" t-value="picking.car_num" />
                                            <t t-set="driver_name" t-value="picking.driver_name" />
                                            <t t-set="default_code" t-value="picking_line.product_id.default_code" />
                                            <t t-set="uom_name" t-value="picking_line.product_uom.name if picking_line.product_uom else ''" />
                                            <tr>
                                                <td class="text-center">
                                                    <t t-esc="(picking.date_done and picking.date_done.date()) or ''"/>
                                                </td>                                                  
                                                <td class="text-center"><t t-esc="location_name"/></td>
                                                <td class="text-center"><t t-esc="bags_count"/></td>
                                                <td class="text-center"><t t-esc="uom_name"/></td>
                                                <td class="text-center"><t t-esc="product_uom_qty"/></td>
                                                <td class="text-center"><t t-esc="weighing_card"/></td>
                                                <td class="text-center"><t t-esc="routing_card"/></td>
                                                <td class="text-center"><t t-esc="transport_contractor_name"/></td>
                                                <td class="text-center"><t t-esc="car_num"/></td>
                                                <td class="text-center"><t t-esc="driver_name"/></td>
                                                <td class="text-center"><t t-esc="default_code"/></td>
                                            </tr>
                                        </t>    
                                    </t>
                                </tbody>
                            </table>
                        
                            <table class="table table-sm table-bordered" style="width: 50%; font-size: 13px; margin-left: auto;">
                                <thead>
                                </thead>
                                <tbody>
                                    <t t-set="quant_out_today" 
                                        t-value="sum(move.quantity_done 
                                                for picking in docs.filtered(lambda picking: picking.date_done 
                                                and picking.date_done.date() == today_date.date()) 
                                                for move in picking.move_ids_without_package)" />
                                    <t t-set="quant_out_except_today" 
                                        t-value="sum(move.quantity_done 
                                                for picking in docs.filtered(lambda picking: picking.date_done 
                                                and picking.date_done.date() != today_date.date()) 
                                                for move in picking.move_ids_without_package)" />
                                    <t t-set="quant_total_out" t-value="quant_out_today + quant_out_except_today" />
                                    <t t-set="quant_total_not_out" t-value="total_qty - quant_out_today - quant_out_except_today" />
                                    <tr>
                                        <td class="text-center">
                                            <t t-if="quant_out_today">
                                                <t t-esc="quant_out_today"/>
                                            </t>
                                            <t t-else="">
                                                0
                                            </t>
                                        </td>
                                        <th class="text-center">اجمالي الكمية المنصرفة اليوم</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center">
                                            <t t-if="quant_out_except_today">
                                                <t t-esc="quant_out_except_today"/>
                                            </t>
                                            <t t-else="">
                                                0
                                            </t>
                                        </td>
                                        <th class="text-center">المنصرف ما قبله</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center">
                                            <t t-if="quant_total_out">
                                                <t t-esc="quant_total_out"/>
                                            </t>
                                            <t t-else="">
                                                0
                                            </t>
                                        </td>
                                        <th class="text-center">اجمالي المنصرف</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center">
                                            <t t-if="quant_total_not_out">
                                                <t t-esc="quant_total_not_out"/>
                                            </t>
                                            <t t-else="">
                                                0
                                            </t>
                                        </td>
                                        <th class="text-center">الرصيد المتبقي من الافراج</th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                
                </div>
            </t>
        </t>    

    </template>
</odoo>
