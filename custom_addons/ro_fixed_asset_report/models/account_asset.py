# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime,date,timedelta
from dateutil.relativedelta import relativedelta


class AccountAsset(models.Model):
    _inherit = "account.asset"

    quantity = fields.Float()
    
    isSold = fields.Boolean('is asset sold', default=False)

    prorata_date = fields.Date(  # the starting date of the depreciations
        string='Prorata Date',
        store=True, readonly=False,
        copy=True,
    )
# compute='_compute_prorata_date', 

    # @api.depends('acquisition_date', 'company_id', 'prorata_computation_type')
    def _compute_prorata_date(self):
        pass
        # for asset in self:

        #     if asset.prorata_computation_type == 'none' and asset.acquisition_date:
        #         fiscalyear_date = asset.company_id.compute_fiscalyear_dates(asset.acquisition_date).get('date_from')
        #         asset.prorata_date = fiscalyear_date
        #     else:
        #         asset.prorata_date = asset.acquisition_date

    
    def set_to_close(self, invoice_line_ids, date=None, message=None):
        super(AccountAsset, self).set_to_close(invoice_line_ids, date, message)
        
        for ass in self:
            if invoice_line_ids:
                ass.isSold = True
                
    def set_to_running(self):
        super(AccountAsset, self).set_to_running()
        
        for ass in self:
            ass.isSold = False
