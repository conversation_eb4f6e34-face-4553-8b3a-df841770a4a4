from odoo import models, fields, _
import base64
from . import xls
from odoo.exceptions import UserError
from itertools import groupby
from operator import itemgetter

extra_space = 5


# a Transient model to get data parameters
class WizardFixedAsset(models.TransientModel):
    _name = 'fixed.asset.getter.wizard'

    report_type = fields.Selection([('simple','Total'),('detailed','Detailed')], default="simple", required=True)
    date = fields.Date(default=fields.Date.context_today, required=True)

    def _get_assets_data(self, report_type, date):
        vals = []
        total_vals = []
        assets = self.env['account.asset']
        
        # filter move data
        assets = assets.search([('state', 'not in', ('draft', 'cancelled')), ('acquisition_date', '<=', date),
                                ('isSold', '=', False) , ('account_asset_id.account_type', '=', 'asset_fixed')
                                , ('asset_type', '=', 'purchase'), ('state', '!=', 'model'), ('parent_id', '=', False)])

        print({"assets": assets})

        # handle empty result
        if len(assets) == 0:
            raise UserError('Timeframe have no data')
            
        total_cost = total_depr = total_accu = total_net = 0
        if report_type == 'detailed':
            assets = assets.sorted(lambda x:x.account_asset_id)

            for asset in assets:

                line_depr = asset.depreciation_move_ids.filtered(lambda x:x.date==date)
                # cost = asset.x_studio_cost or asset.original_value
                cost = asset.original_value

                accu_depr = cost - line_depr[0].asset_remaining_value if len(line_depr)>0 else cost
                net_value = line_depr[0].asset_remaining_value if len(line_depr)>0 else 0
                if (asset.account_asset_id.is_not_depreciable and net_value == 0) or ( asset.prorata_date > date):
                    net_value = cost
                    accu_depr = 0

                vals.append({
                    'Description': asset.name or '',
                    'Acquisition Date': str(asset.acquisition_date.strftime('%Y-%m-%d')) or '',
                    'Bill Date': str(asset.original_move_line_ids[0].date.strftime('%Y-%m-%d')) if len(asset.original_move_line_ids)>0 else '',
                    'Quantity': asset.quantity or '',
                    'Major Category': asset.account_asset_id.name or '',
                    'Cost': cost or 0,
                    'Deprn Amount': line_depr[0].depreciation_value if len(line_depr)>0 else 0 ,
                    'ACCU. DEPRN': accu_depr,
                    'Net Value': net_value
                })
                total_cost += vals[-1]['Cost']
                total_depr += vals[-1]['Deprn Amount']
                total_accu += vals[-1]['ACCU. DEPRN']
                total_net += vals[-1]['Net Value']
                columns = []
                for key in vals[0].keys():
                    width = len(key) + extra_space
                    columns.append((key,width))

        if report_type == 'simple':

            for asset in assets:
                
                line_depr = asset.depreciation_move_ids.filtered(lambda x:x.date==date)
                # cost = asset.x_studio_cost or asset.original_value
                cost =  asset.original_value
                print(f"for asset {asset} the line depr {line_depr}")
                accu_depr = cost - line_depr[0].asset_remaining_value if len(line_depr)>0 else cost
                actual_accu_depr = cost - (  line_depr[0].asset_remaining_value if len(line_depr)>0 else cost )
                net_value = line_depr[0].asset_remaining_value if len(line_depr)>0 else 0
                actual_net_value = line_depr[0].asset_remaining_value if len(line_depr)>0 else cost
                print(f"for asset {asset} the accu_depr {accu_depr}")
                print(f"for asset {asset} the net_value {net_value}")
                print(f"for asset {asset} the actual_accu_depr {actual_accu_depr}")
                print(f"for asset {asset} the actual_net_value {actual_net_value}")
                
                if (asset.account_asset_id.is_not_depreciable and net_value == 0) or ( asset.prorata_date > date):
                    net_value = cost
                    accu_depr = 0

                vals.append({
                    'Description': asset.name or '',
                    'Major Category': asset.account_asset_id.name or '',
                    'Cost': cost or 0,
                    'Deprn Amount': line_depr[0].depreciation_value if len(line_depr)>0 else 0 ,
                    'ACCU. DEPRN': accu_depr,
                    'Net Value': net_value
                })

            vals = sorted(vals, 
                            key = itemgetter('Major Category'))
            
            # Display data grouped by Major Category
            for key, value in groupby(vals,
                                    key = itemgetter('Major Category')):
                cost = depr_amt = accu_depr = net_value = 0

                for k in value:
                    cost += k['Cost']
                    depr_amt += k['Deprn Amount']
                    accu_depr += k['ACCU. DEPRN']
                    net_value += k['Net Value']

                total_vals.append({
                    'Major Category': key or '',
                    'Cost': cost or 0,
                    'Deprn Amount': depr_amt or 0,
                    'ACCU. DEPRN': accu_depr,
                    'Net Value': net_value,
                })
                total_cost += total_vals[-1]['Cost']
                total_depr += total_vals[-1]['Deprn Amount']
                total_accu += total_vals[-1]['ACCU. DEPRN']
                total_net += total_vals[-1]['Net Value']
                columns = []
                for key in total_vals[0].keys():
                    width = len(key) + extra_space
                    columns.append((key,width))
            vals = total_vals

            # accounts = assets.mapped('account_asset_id')
            # for account in accounts:
            #     account_assets = assets.filtered(lambda x:x.account_asset_id == account)

            #     line_depr = account_assets.depreciation_move_ids.filtered(lambda x:x.date==date)                
            #     cost = account_assets.mapped(lambda x:x.x_studio_cost or x.original_value)

            #     accu_depr = sum(cost) - sum(line_depr.mapped('asset_remaining_value'))
            #     net_value = sum(line_depr.mapped('asset_remaining_value'))

            #     if (account.is_not_depreciable and net_value == 0) :
            #         net_value = sum(cost)
            #         accu_depr = 0

            #     vals.append({
            #         'Major Category': account.name or '',
            #         'Cost': sum(cost) or 0,
            #         'Deprn Amount': sum(line_depr.mapped('depreciation_value')) or 0,
            #         'ACCU. DEPRN': accu_depr,
            #         'Net Value': net_value,
            #     })
            #     total_cost += vals[-1]['Cost']
            #     total_depr += vals[-1]['Deprn Amount']
            #     total_accu += vals[-1]['ACCU. DEPRN']
            #     total_net += vals[-1]['Net Value']
            #     columns = []
            #     for key in vals[0].keys():
            #         width = len(key) + extra_space
            #         columns.append((key,width))
        data=False
        if len(vals) >0:
            vals_keys = list(vals[0].keys())
            group = [{
                    'lines': vals, 
                    'sums': [(vals_keys.index('Cost'), total_cost),
                            (vals_keys.index("Deprn Amount"), total_depr),
                            (vals_keys.index("ACCU. DEPRN"), total_accu),
                            (vals_keys.index('Net Value'), total_net),
                            ],
                }]
        
            data = {'groups': group, 'columns': columns, 'date': date}
        return data

    def _return_file_notification(self, file_name, file):
        attachment_id = self.env['fixed.asset.file.wizard'].create({
            'file_name': file_name,
            'report_file': base64.b64encode(file),
        })
        return {
            'name': _('Notification'),
            'context': self.env.context,
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'fixed.asset.file.wizard',
            'res_id': attachment_id.id,
            'data': None,
            'type': 'ir.actions.act_window',
            'target': 'new'
        }
    
    def get_excel_report(self):
        data = self._get_assets_data(self.report_type, self.date)

        if not data:
            raise UserError("No data found")
        
        if self.report_type == 'detailed':
            report_name = "Fixed Assets Detailed Report"
        elif self.report_type == 'simple':
            report_name = "Fixed Assets Totals Report"
        else:
            report_name = "NONe"
        file_name = f'{report_name}.xlsx'
        xlsx = xls.get_excel(report_name, data)
        return self._return_file_notification(file_name, xlsx)





