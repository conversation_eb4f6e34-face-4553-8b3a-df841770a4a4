from io import BytesIO
import xlsxwriter
from datetime import datetime


def add_workbook_format(workbook):
    colors = {
        'white_orange': '#FFFFDB',
        'orange': '#FFC300',
        'red': '#FF0000',
        'yellow': '#F6FA03',
    }

    wbf = {}
    wbf['header'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': '#FFFFDB', 'font_color': '#000000', 'font_name': 'Georgia'})
    wbf['header'].set_border()

    wbf['header_orange'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': colors['orange'], 'font_color': '#000000',
         'font_name': 'Georgia'})
    wbf['header_orange'].set_border()

    wbf['header_yellow'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': colors['yellow'], 'font_color': '#000000',
         'font_name': 'Georgia'})
    wbf['header_yellow'].set_border()

    wbf['header_no'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': '#FFFFDB', 'font_color': '#000000', 'font_name': 'Georgia'})
    wbf['header_no'].set_border()
    wbf['header_no'].set_align('vcenter')

    wbf['footer'] = workbook.add_format({'align': 'left', 'font_name': 'Georgia'})

    wbf['content_datetime'] = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss', 'font_name': 'Georgia'})
    wbf['content_datetime'].set_left()
    wbf['content_datetime'].set_right()

    wbf['content_date'] = workbook.add_format({'num_format': 'yyyy-mm-dd', 'font_name': 'Georgia'})
    wbf['content_date'].set_left()
    wbf['content_date'].set_right()

    wbf['title_doc'] = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 20,
        'font_name': 'Georgia',
    })

    wbf['company'] = workbook.add_format({'align': 'left', 'font_name': 'Georgia'})
    wbf['company'].set_font_size(11)

    wbf['content'] = workbook.add_format()
    wbf['content'].set_left()
    wbf['content'].set_right()

    wbf['content_float'] = workbook.add_format({'align': 'right', 'num_format': '#,##0.00', 'font_name': 'Georgia'})
    wbf['content_float'].set_right()
    wbf['content_float'].set_left()

    wbf['content_number'] = workbook.add_format({'align': 'right', 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['content_number'].set_right()
    wbf['content_number'].set_left()

    wbf['content_percent'] = workbook.add_format({'align': 'right', 'num_format': '0.00%', 'font_name': 'Georgia'})
    wbf['content_percent'].set_right()
    wbf['content_percent'].set_left()

    wbf['total_float'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['white_orange'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float'].set_top()
    wbf['total_float'].set_bottom()
    wbf['total_float'].set_left()
    wbf['total_float'].set_right()

    wbf['total_number'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['white_orange'], 'bold': 1, 'num_format': '#,##0',
         'font_name': 'Georgia'})
    wbf['total_number'].set_top()
    wbf['total_number'].set_bottom()
    wbf['total_number'].set_left()
    wbf['total_number'].set_right()

    wbf['total'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['white_orange'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total'].set_left()
    wbf['total'].set_right()
    wbf['total'].set_top()
    wbf['total'].set_bottom()

    wbf['total_float_yellow'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['yellow'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float_yellow'].set_top()
    wbf['total_float_yellow'].set_bottom()
    wbf['total_float_yellow'].set_left()
    wbf['total_float_yellow'].set_right()

    wbf['total_number_yellow'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['yellow'], 'bold': 1, 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['total_number_yellow'].set_top()
    wbf['total_number_yellow'].set_bottom()
    wbf['total_number_yellow'].set_left()
    wbf['total_number_yellow'].set_right()

    wbf['total_yellow'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['yellow'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total_yellow'].set_left()
    wbf['total_yellow'].set_right()
    wbf['total_yellow'].set_top()
    wbf['total_yellow'].set_bottom()

    wbf['total_float_orange'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['orange'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float_orange'].set_top()
    wbf['total_float_orange'].set_bottom()
    wbf['total_float_orange'].set_left()
    wbf['total_float_orange'].set_right()

    wbf['total_number_orange'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['orange'], 'bold': 1, 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['total_number_orange'].set_top()
    wbf['total_number_orange'].set_bottom()
    wbf['total_number_orange'].set_left()
    wbf['total_number_orange'].set_right()

    wbf['total_orange'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['orange'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total_orange'].set_left()
    wbf['total_orange'].set_right()
    wbf['total_orange'].set_top()
    wbf['total_orange'].set_bottom()

    wbf['header_detail_space'] = workbook.add_format({'font_name': 'Georgia'})
    wbf['header_detail_space'].set_left()
    wbf['header_detail_space'].set_right()
    wbf['header_detail_space'].set_top()
    wbf['header_detail_space'].set_bottom()

    wbf['header_detail'] = workbook.add_format({'bg_color': '#E0FFC2', 'font_name': 'Georgia'})
    wbf['header_detail'].set_left()
    wbf['header_detail'].set_right()
    wbf['header_detail'].set_top()
    wbf['header_detail'].set_bottom()

    return wbf


def get_excel(report_name, data):
    fp = BytesIO()
    workbook = xlsxwriter.Workbook(fp)
    wbf = add_workbook_format(workbook)
    worksheet = workbook.add_worksheet(report_name)
    if not data:
        workbook.close()
        return fp.getvalue()

    row = 2
    length = len(data['columns'])

    worksheet.write(row, 0, 'Date', wbf['header_orange'])
    worksheet.write(row, 1, str(data['date'].strftime('%Y-%m-%d')), wbf['content'])

        
    row += 3
    
    for group in data['groups']:
        if group.get('name', False):
            worksheet.merge_range('A' + str(row) + ':B' + str(row), group['name'], wbf['header_yellow'])
        col = 0
        for column in data['columns']:
            column_name = column[0]
            column_width = column[1]
            worksheet.set_column(col, col, column_width)
            worksheet.write(row, col, column_name, wbf['header_orange'])
            worksheet.write(row, col, column_name, wbf['header_orange'])
            col += 1
        
        for line in group['lines']:
            row += 1
            col = 0
            for key in line.keys():
                worksheet.write(row, col, line[key], wbf['content'])
                col += 1
                
        row += 1
        index = 1000
        for sum in group['sums']:
            worksheet.write(row,sum[0], sum[1],wbf['total'])
            index = min(sum[0], index)
            
        worksheet.merge_range(row, 0, row, index-1, 'Total', wbf['total'])
        row += 2
    
    
    workbook.close()
    return fp.getvalue()